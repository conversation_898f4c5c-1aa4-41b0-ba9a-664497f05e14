import { test, expect } from '@playwright/test'

test.describe('Landing Page', () => {
  test('should load the landing page successfully', async ({ page }) => {
    await page.goto('/')
    
    // Check that the page loads
    await expect(page).toHaveTitle(/KaziSync/i)
    
    // Check for main heading
    await expect(page.getByRole('heading', { level: 1 })).toBeVisible()
  })

  test('should have working navigation', async ({ page }) => {
    await page.goto('/')
    
    // Check navigation links
    await expect(page.getByRole('link', { name: /features/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /pricing/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /about/i })).toBeVisible()
    await expect(page.getByRole('link', { name: /contact/i })).toBeVisible()
  })

  test('should navigate to login page', async ({ page }) => {
    await page.goto('/')
    
    // Click login button
    await page.getByRole('link', { name: /sign in/i }).click()
    
    // Should be on login page
    await expect(page).toHaveURL('/login')
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible()
  })

  test('should navigate to signup page', async ({ page }) => {
    await page.goto('/')
    
    // Click signup button
    await page.getByRole('link', { name: /get started/i }).first().click()
    
    // Should be on signup page
    await expect(page).toHaveURL('/signup')
    await expect(page.getByRole('heading', { name: /sign up/i })).toBeVisible()
  })

  test('should display hero section', async ({ page }) => {
    await page.goto('/')
    
    // Check for hero content
    await expect(page.getByText(/smarter HRMS/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /get started/i })).toBeVisible()
  })

  test('should display features section', async ({ page }) => {
    await page.goto('/')
    
    // Scroll to features section
    await page.getByText(/features/i).first().scrollIntoViewIfNeeded()
    
    // Check for feature cards
    await expect(page.getByText(/attendance/i)).toBeVisible()
    await expect(page.getByText(/leave/i)).toBeVisible()
    await expect(page.getByText(/payroll/i)).toBeVisible()
  })

  test('should display pricing section', async ({ page }) => {
    await page.goto('/')
    
    // Scroll to pricing section
    await page.getByText(/pricing/i).first().scrollIntoViewIfNeeded()
    
    // Check for pricing cards
    await expect(page.getByText(/starter/i)).toBeVisible()
    await expect(page.getByText(/professional/i)).toBeVisible()
    await expect(page.getByText(/enterprise/i)).toBeVisible()
  })

  test('should have responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')
    
    // Check that mobile menu toggle is visible
    await expect(page.getByRole('button', { name: /menu/i })).toBeVisible()
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.goto('/')
    
    // Check that navigation is visible
    await expect(page.getByRole('navigation')).toBeVisible()
  })

  test('should have working contact form', async ({ page }) => {
    await page.goto('/')
    
    // Scroll to contact section
    await page.getByText(/contact/i).first().scrollIntoViewIfNeeded()
    
    // Fill out contact form
    await page.getByLabel(/name/i).fill('Test User')
    await page.getByLabel(/email/i).fill('<EMAIL>')
    await page.getByLabel(/message/i).fill('This is a test message')
    
    // Submit form (note: this might need to be adjusted based on actual implementation)
    await page.getByRole('button', { name: /send/i }).click()
    
    // Check for success message or form submission
    await expect(page.getByText(/thank you/i)).toBeVisible({ timeout: 10000 })
  })

  test('should load without accessibility violations', async ({ page }) => {
    await page.goto('/')
    
    // Basic accessibility checks
    await expect(page.getByRole('main')).toBeVisible()
    await expect(page.getByRole('navigation')).toBeVisible()
    await expect(page.getByRole('contentinfo')).toBeVisible()
    
    // Check for proper heading hierarchy
    const h1 = page.getByRole('heading', { level: 1 })
    await expect(h1).toBeVisible()
  })
})
