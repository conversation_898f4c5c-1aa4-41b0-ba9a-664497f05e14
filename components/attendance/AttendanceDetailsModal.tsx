'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface AttendanceRecord {
  attendance_id: string;
  check_in_time: string | null;
  check_out_time: string | null;
  created_at: string;
  created_by: string | null;
  date: string;
  employee_id: string;
  expected_end_time: string | null;
  expected_start_time: string | null;
  is_overtime: boolean | null;
  notes: string | null;
  overtime_hours: number | null;
  shift_id: string | null;
  source: string;
  source_record_id: string | null;
  status: string;
  total_hours: number | null;
  updated_at: string;
}



interface AttendanceDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  attendanceId: string | null;
  employeeName: string;
}

const AttendanceDetailsModal: React.FC<AttendanceDetailsModalProps> = ({
  isOpen,
  onClose,
  attendanceId,
  employeeName
}) => {
  const { companies } = useAuth();
  const [attendanceRecord, setAttendanceRecord] = useState<AttendanceRecord | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen && attendanceId) {
      fetchAttendanceDetails();
    }
  }, [isOpen, attendanceId]);

  const fetchAttendanceDetails = async () => {
    if (!attendanceId) return;

    try {
      setIsLoading(true);
      setError('');

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      // Fetch attendance details
      const response = await apiGet<{attendance_record: AttendanceRecord, status: string}>(
        `api/attendance/${attendanceId}?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.attendance_record) {
        setAttendanceRecord(response.attendance_record);
      } else {
        throw new Error('Failed to fetch attendance details');
      }
    } catch (error: unknown) {
      setError((error as Error)?.message || 'Failed to fetch attendance details');
    } finally {
      setIsLoading(false);
    }
  };

  // Format time for display
  const formatTime = (timeString: string | null) => {
    if (!timeString) return 'N/A';

    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    } catch {
      return timeString;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  // Format hours for display
  const formatHours = (hours: number | null) => {
    if (hours === null) return 'N/A';

    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);

    return `${wholeHours}h ${minutes}m`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              onClick={onClose}
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Attendance Details
                </h3>

                {error && (
                  <div className="mt-2 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {isLoading ? (
                  <div className="mt-4 flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : attendanceRecord ? (
                  <div className="mt-4 space-y-4">
                    <div className="flex items-center">
                      <div className="h-12 w-12 rounded-full bg-primary text-white flex items-center justify-center mr-4">
                        <span className="text-lg font-medium">
                          {employeeName.split(' ').map(name => name[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <h4 className="text-xl font-medium text-secondary-dark">{employeeName}</h4>
                        <p className="text-sm text-secondary">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            attendanceRecord.status === 'present'
                              ? 'bg-green-100 text-green-800'
                              : attendanceRecord.status === 'absent'
                              ? 'bg-red-100 text-red-800'
                              : attendanceRecord.status === 'late'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {attendanceRecord.status.charAt(0).toUpperCase() + attendanceRecord.status.slice(1)}
                          </span>
                          {' '} on {formatDate(attendanceRecord.date)}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                      <div>
                        <p className="text-xs text-secondary">Check In Time</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {formatTime(attendanceRecord.check_in_time)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Check Out Time</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {formatTime(attendanceRecord.check_out_time)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Total Hours</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {formatHours(attendanceRecord.total_hours)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Source</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {attendanceRecord.source}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Expected Start Time</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {formatTime(attendanceRecord.expected_start_time)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Expected End Time</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {formatTime(attendanceRecord.expected_end_time)}
                        </p>
                      </div>
                      <div className="col-span-2">
                        <p className="text-xs text-secondary">Notes</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {attendanceRecord.notes || 'No notes available'}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Overtime</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {attendanceRecord.is_overtime ? 'Yes' : 'No'}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Overtime Hours</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {formatHours(attendanceRecord.overtime_hours)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Created At</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {formatDate(attendanceRecord.created_at)} {formatTime(attendanceRecord.created_at)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Updated At</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {formatDate(attendanceRecord.updated_at)} {formatTime(attendanceRecord.updated_at)}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="mt-4 text-center text-secondary">
                    No attendance data found
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AttendanceDetailsModal;
