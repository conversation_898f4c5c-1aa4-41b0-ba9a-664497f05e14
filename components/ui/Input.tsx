import React from 'react';

type InputSize = 'sm' | 'md' | 'lg';
type InputState = 'default' | 'error' | 'success';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  success?: string;
  hint?: string;
  size?: InputSize;
  state?: InputState;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  required?: boolean;
}

const getInputClasses = (
  size: InputSize = 'md',
  state: InputState = 'default',
  hasLeftIcon: boolean = false,
  hasRightIcon: boolean = false,
  className?: string
) => {
  const baseClasses = 'w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-0 transition-colors disabled:opacity-50 disabled:cursor-not-allowed';
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm min-h-[36px]',
    md: 'px-4 py-3 text-sm min-h-[44px]',
    lg: 'px-4 py-4 text-base min-h-[48px]',
  };

  const stateClasses = {
    default: 'border-gray-300 hover:border-gray-400 focus:border-primary focus:ring-primary-500',
    error: 'border-danger bg-danger-50 focus:border-danger focus:ring-danger-500',
    success: 'border-success bg-success-50 focus:border-success focus:ring-success-500',
  };

  const paddingClasses = {
    left: hasLeftIcon ? 'pl-10' : '',
    right: hasRightIcon ? 'pr-10' : '',
  };

  return `${baseClasses} ${sizeClasses[size]} ${stateClasses[state]} ${paddingClasses.left} ${paddingClasses.right} ${className || ''}`.trim();
};

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    label,
    error,
    success,
    hint,
    size = 'md',
    state = 'default',
    leftIcon,
    rightIcon,
    required = false,
    className,
    id,
    ...props 
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const errorId = error ? `${inputId}-error` : undefined;
    const successId = success ? `${inputId}-success` : undefined;
    const hintId = hint ? `${inputId}-hint` : undefined;
    
    // Determine state based on error/success props
    const currentState = error ? 'error' : success ? 'success' : state;

    const describedBy = [errorId, successId, hintId].filter(Boolean).join(' ') || undefined;

    return (
      <div className="space-y-2">
        {label && (
          <label 
            htmlFor={inputId} 
            className="block text-sm font-medium text-text-primary"
          >
            {label}
            {required && (
              <span className="text-danger ml-1" aria-label="required">*</span>
            )}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400" aria-hidden="true">
                {leftIcon}
              </span>
            </div>
          )}
          
          <input
            ref={ref}
            id={inputId}
            className={getInputClasses(size, currentState, !!leftIcon, !!rightIcon, className)}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={describedBy}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span className="text-gray-400" aria-hidden="true">
                {rightIcon}
              </span>
            </div>
          )}
        </div>

        {error && (
          <p id={errorId} className="text-sm text-danger flex items-center" role="alert">
            <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {error}
          </p>
        )}

        {success && !error && (
          <p id={successId} className="text-sm text-success flex items-center">
            <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            {success}
          </p>
        )}

        {hint && !error && !success && (
          <p id={hintId} className="text-sm text-text-secondary">
            {hint}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input };
