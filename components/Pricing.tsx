"use client";

import React from 'react';
import Link from 'next/link';

const pricingPlans = [
  {
    name: 'Starter',
    basePrice: 49,
    perEmployee: 2.50,
    description: 'Small Teams (<20) - Ideal for small teams just getting started with HR digitization.',
    features: [
      'Real-Time Attendance Tracking',
      'Leave Management',
      'Payroll Management',
      'Centralized Document Storage (Limited)',
      'Email Support',
      'Mobile App Access',
    ],
    excludedFeatures: [
      'Loan & Advance Management',
      'Performance Reviews',
      'Employee Onboarding',
      'HR Analytics Dashboard',
      'QuickBooks Integration',
      'Multi-Device IoT Integration',
    ],
    cta: 'Get Started',
    highlighted: false,
    color: 'green',
  },
  {
    name: 'Growth',
    basePrice: 99,
    perEmployee: 2.00,
    description: 'Mid-size Companies - Perfect for growing teams that need performance tracking and onboarding.',
    features: [
      'Everything in Starter',
      'Loan & Advance Management',
      'Performance Reviews',
      'Employee Onboarding',
      'HR Analytics Dashboard (Basic)',
      'QuickBooks Integration',
      'Multi-Device IoT Integration (Up to 2 Devices)',
      'Custom Roles & Permissions',
      'Email Support',
    ],
    excludedFeatures: [
      'Audit Logs & Compliance Reports',
      'Custom Feature Add-ons',
    ],
    cta: 'Get Started',
    highlighted: true,
    color: 'blue',
  },
  {
    name: 'Pro',
    basePrice: 199,
    perEmployee: 1.50,
    description: 'Large Enterprises - Enterprise-ready with unlimited IoT devices and advanced analytics.',
    features: [
      'Everything in Growth',
      'Centralized Document Storage (Unlimited)',
      'HR Analytics Dashboard (Advanced)',
      'Multi-Device IoT Integration (Unlimited)',
      'Priority Support + Phone',
      'Audit Logs & Compliance Reports',
      'Custom Feature Add-ons',
      'Dedicated Support / SLA',
    ],
    excludedFeatures: [],
    cta: 'Contact Sales',
    highlighted: false,
    color: 'purple',
  },
];

const Pricing = () => {
  return (
    <section id="pricing" className="section bg-white">
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
            Pricing That Grows with You
          </h2>
          <p className="text-secondary text-lg mb-8">
            We offer flexible pricing plans to fit every business size, with a minimum monthly fee + per-employee rate. All plans include core modules with 14-day free trial.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => (
            <div
              key={index}
              className={`card overflow-hidden transition-all duration-300 ${
                plan.highlighted
                  ? 'border-2 border-primary relative transform hover:-translate-y-2'
                  : 'hover:border-gray-300'
              }`}
            >
              {plan.highlighted && (
                <div className="bg-primary text-white text-center py-1 text-sm font-medium">
                  Most Popular
                </div>
              )}
              <div className="p-6 md:p-8">
                <h3 className="text-2xl font-bold text-secondary-dark mb-2">{plan.name}</h3>
                <p className="text-secondary mb-6">{plan.description}</p>
                <div className="mb-6">
                  <div className="text-4xl font-bold text-secondary-dark">
                    ${plan.basePrice}<span className="text-lg">/mo</span>
                  </div>
                  <div className="text-secondary text-sm mt-1">
                    + ${plan.perEmployee}/employee
                  </div>
                </div>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <svg
                        className="h-5 w-5 text-success mt-0.5 mr-2 flex-shrink-0"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span className="text-secondary-dark text-sm">{feature}</span>
                    </li>
                  ))}
                  {plan.excludedFeatures.map((feature, featureIndex) => (
                    <li key={`excluded-${featureIndex}`} className="flex items-start opacity-50">
                      <svg
                        className="h-5 w-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                      <span className="text-gray-500 text-sm line-through">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href={plan.name === 'Pro' ? '/contact' : '/signup'}
                  className={`block w-full text-center py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                    plan.highlighted
                      ? 'bg-primary text-white hover:bg-primary-dark hover:text-white'
                      : 'bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white'
                  }`}
                >
                  {plan.cta}
                </Link>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center max-w-3xl mx-auto">
          <h3 className="text-xl font-semibold text-secondary-dark mb-4">
            Need a custom solution?
          </h3>
          <p className="text-secondary mb-6">
            Contact our sales team for a custom quote tailored to your organization&apos;s specific needs.
          </p>
          <Link href="#contact" className="btn-outline px-8 py-3">
            Contact Sales
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
