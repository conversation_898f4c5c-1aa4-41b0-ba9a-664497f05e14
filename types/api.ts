/**
 * Common API response types for KaziSync
 */

// Base API response structure
export interface ApiResponse<T = unknown> {
  success: boolean
  message?: string
  data?: T
  error?: string
  errors?: Record<string, string[]>
}

// Pagination types
export interface PaginationMeta {
  current_page: number
  per_page: number
  total: number
  last_page: number
  from: number
  to: number
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta?: PaginationMeta
  links?: {
    first: string
    last: string
    prev: string | null
    next: string | null
  }
}

// User and Authentication types
export interface User {
  id: string
  username: string
  email: string
  role: 'hr' | 'employee' | 'manager' | 'super-admin'
  employee_id?: string
  company_id?: string
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  user: User
  companies?: Company[]
}

export interface RegistrationRequest {
  username: string
  email: string
  password: string
  password_confirmation: string
  company_name?: string
  role?: string
}

// Company types
export interface Company {
  id: string
  name: string
  email?: string
  phone?: string
  address?: string
  country?: string
  created_at: string
  updated_at: string
}

// Employee types
export interface Employee {
  id: string
  employee_id: string
  first_name: string
  last_name: string
  email: string
  phone?: string
  department_id?: string
  department?: Department
  position?: string
  hire_date?: string
  status: 'active' | 'inactive' | 'terminated'
  company_id: string
  created_at: string
  updated_at: string
}

// Department types
export interface Department {
  id: string
  name: string
  description?: string
  company_id: string
  created_at: string
  updated_at: string
}

// Attendance types
export interface AttendanceRecord {
  id: string
  employee_id: string
  employee?: Employee
  date: string
  clock_in?: string
  clock_out?: string
  break_start?: string
  break_end?: string
  total_hours?: number
  status: 'present' | 'absent' | 'late' | 'early_departure'
  notes?: string
  created_at: string
  updated_at: string
}

export interface AttendanceStatistics {
  total_days: number
  present_days: number
  absent_days: number
  late_days: number
  early_departures: number
  total_hours: number
  average_hours: number
  attendance_rate: number
}

// Leave types
export interface LeaveType {
  id: string
  name: string
  code: string
  days_per_year: number
  requires_approval: boolean
  requires_documentation: boolean
  company_id: string
  created_at: string
  updated_at: string
}

export interface LeaveRequest {
  id: string
  employee_id: string
  employee?: Employee
  leave_type_id: string
  leave_type?: LeaveType
  start_date: string
  end_date: string
  days_requested: number
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  approved_by?: string
  approved_at?: string
  rejection_reason?: string
  created_at: string
  updated_at: string
}

// Error types
export interface ValidationError {
  field: string
  message: string
}

export interface ApiError {
  message: string
  code?: string | number
  details?: ValidationError[]
}

// Generic request types
export interface CreateRequest<T = Record<string, unknown>> {
  [key: string]: unknown
}

export interface UpdateRequest<T = Record<string, unknown>> {
  [key: string]: unknown
}

// Filter and search types
export interface FilterParams {
  page?: number
  per_page?: number
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  [key: string]: unknown
}

// Chart and analytics data types
export interface ChartDataPoint {
  label: string
  value: number
  color?: string
}

export interface TimeSeriesDataPoint {
  date: string
  value: number
  label?: string
}

export interface AnalyticsData {
  total: number
  change: number
  change_percentage: number
  trend: 'up' | 'down' | 'stable'
  chart_data?: ChartDataPoint[]
  time_series?: TimeSeriesDataPoint[]
}

// File upload types
export interface FileUploadResponse {
  filename: string
  original_name: string
  size: number
  mime_type: string
  url: string
}

// Notification types
export interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  created_at: string
}

// Export all types for easy importing
export type {
  ApiResponse,
  PaginatedResponse,
  PaginationMeta,
  User,
  LoginRequest,
  LoginResponse,
  RegistrationRequest,
  Company,
  Employee,
  Department,
  AttendanceRecord,
  AttendanceStatistics,
  LeaveType,
  LeaveRequest,
  ValidationError,
  ApiError,
  CreateRequest,
  UpdateRequest,
  FilterParams,
  ChartDataPoint,
  TimeSeriesDataPoint,
  AnalyticsData,
  FileUploadResponse,
  Notification,
}
