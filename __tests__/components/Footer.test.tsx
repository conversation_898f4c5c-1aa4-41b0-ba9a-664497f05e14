import { render, screen } from '@testing-library/react'
import Footer from '@/components/Footer'

describe('Footer Component', () => {
  it('renders the company name', () => {
    render(<Footer />)
    
    expect(screen.getByText('KaziSync')).toBeInTheDocument()
  })
  
  it('displays the current year in copyright', () => {
    render(<Footer />)
    
    const currentYear = new Date().getFullYear()
    expect(screen.getByText(`© ${currentYear} KaziSync. All rights reserved.`)).toBeInTheDocument()
  })
  
  it('contains navigation links', () => {
    render(<Footer />)

    expect(screen.getByText('Features')).toBeInTheDocument()
    expect(screen.getByText('Pricing')).toBeInTheDocument()
    expect(screen.getByText('Benefits')).toBeInTheDocument()
    expect(screen.getByText('Contact')).toBeInTheDocument()
  })
  
  it('displays company description', () => {
    render(<Footer />)
    
    expect(screen.getByText('A smarter HRMS for modern businesses. Registered in Wyoming, USA.')).toBeInTheDocument()
  })
  
  it('contains social media links', () => {
    render(<Footer />)
    
    // Check for social media icons/links (adjust based on actual implementation)
    const socialLinks = screen.getAllByRole('link')
    expect(socialLinks.length).toBeGreaterThan(0)
  })
  
  it('has proper accessibility structure', () => {
    render(<Footer />)
    
    const footer = screen.getByRole('contentinfo')
    expect(footer).toBeInTheDocument()
  })
})
