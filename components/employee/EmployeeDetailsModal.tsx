'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
  id_number: string | null;
}

interface EmployeeDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  employeeId: string | null;
}

const EmployeeDetailsModal: React.FC<EmployeeDetailsModalProps> = ({
  isOpen,
  onClose,
  employeeId
}) => {
  const { companies } = useAuth();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [department, setDepartment] = useState<Department | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen && employeeId) {
      fetchEmployeeDetails();
    }
  }, [isOpen, employeeId]);

  const fetchEmployeeDetails = async () => {
    if (!employeeId) return;

    try {
      setIsLoading(true);
      setError('');

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      // Fetch employee details
      const response = await apiGet<{code: number, extend: {employee: Employee}, msg: string}>(`api/employees/${employeeId}?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });



      // Check if the response has the expected structure
      if (response.code === 100 && response.extend && response.extend.employee) {
        setEmployee(response.extend.employee);

        // If employee has a department, fetch department details
        if (response.extend.employee.department_id) {
          try {
            const departmentResponse = await apiGet<{department: Department, success: boolean}>(`api/departments/${response.extend.employee.department_id}?company_id=${companyId}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });



            if (departmentResponse.department) {
              setDepartment(departmentResponse.department);
            }
          } catch (deptError) {

            // Continue without department info
          }
        }
      } else {
        throw new Error('Failed to fetch employee details');
      }
    } catch (error: unknown) {

      setError(error.message || 'Failed to fetch employee details');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              onClick={onClose}
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Employee Details
                </h3>

                {error && (
                  <div className="mt-2 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {isLoading ? (
                  <div className="mt-4 flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : employee ? (
                  <div className="mt-4 space-y-4">
                    <div className="flex items-center">
                      <div className="h-16 w-16 rounded-full bg-primary text-white flex items-center justify-center mr-4">
                        <span className="text-xl font-medium">
                          {employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <h4 className="text-xl font-medium text-secondary-dark">{employee.full_name}</h4>
                        <p className="text-sm text-secondary">{employee.position || 'No position specified'}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                      <div>
                        <p className="text-xs text-secondary">ID Number</p>
                        <p className="text-sm font-medium text-secondary-dark">{employee.id_number || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Email</p>
                        <p className="text-sm font-medium text-secondary-dark">{employee.email || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Phone</p>
                        <p className="text-sm font-medium text-secondary-dark">{employee.phone_number || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Department</p>
                        <p className="text-sm font-medium text-secondary-dark">{department?.name || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Status</p>
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          employee.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {employee.status === 'active' ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Hire Date</p>
                        <p className="text-sm font-medium text-secondary-dark">{employee.hire_date || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-xs text-secondary">Created At</p>
                        <p className="text-sm font-medium text-secondary-dark">
                          {new Date(employee.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="mt-4 text-center text-secondary">
                    No employee data found
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeDetailsModal;
