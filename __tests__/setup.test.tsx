import { render, screen } from '@testing-library/react'

// Simple test to verify Jest setup is working
describe('Jest Setup', () => {
  it('should render a simple component', () => {
    const TestComponent = () => <div>Test Component</div>
    
    render(<TestComponent />)
    
    expect(screen.getByText('Test Component')).toBeInTheDocument()
  })
  
  it('should handle basic assertions', () => {
    expect(1 + 1).toBe(2)
    expect('hello').toMatch(/hello/)
    expect([1, 2, 3]).toHaveLength(3)
  })
})
